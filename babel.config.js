module.exports = function(api) {
  api.cache(true);

  return {
    presets: [
      ['babel-preset-react-app', {
        runtime: 'automatic'
      }]
    ],
    plugins: [
      '@babel/plugin-proposal-optional-chaining',
      '@babel/plugin-proposal-nullish-coalescing-operator'
    ],
    overrides: [
      {
        test: /node_modules\/@mui/,
        presets: [
          ['@babel/preset-env', {
            targets: {
              browsers: ['>0.2%', 'not dead', 'not ie <= 11', 'not op_mini all']
            }
          }]
        ],
        plugins: [
          '@babel/plugin-proposal-optional-chaining',
          '@babel/plugin-proposal-nullish-coalescing-operator'
        ]
      }
    ]
  };
};
