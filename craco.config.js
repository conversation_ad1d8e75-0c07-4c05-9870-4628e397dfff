module.exports = {
  babel: {
    plugins: [
      '@babel/plugin-proposal-optional-chaining',
      '@babel/plugin-proposal-nullish-coalescing-operator',
      '@babel/plugin-proposal-class-properties'
    ]
  },
  webpack: {
    configure: (webpackConfig) => {
      // Find the babel-loader rule
      const babelLoaderRule = webpackConfig.module.rules.find(rule =>
        rule.oneOf && rule.oneOf.some(oneOfRule =>
          oneOfRule.loader && oneOfRule.loader.includes('babel-loader')
        )
      );

      if (babelLoaderRule) {
        const babelRule = babelLoaderRule.oneOf.find(oneOfRule =>
          oneOfRule.loader && oneOfRule.loader.includes('babel-loader')
        );

        if (babelRule) {
          // Modify the include to process @mui modules and chartjs-plugin-trendline
          babelRule.include = [
            babelRule.include,
            /node_modules\/@mui/,
            /node_modules\/@emotion/,
            /node_modules\/chartjs-plugin-trendline/
          ].filter(Boolean);
        }
      }

      return webpackConfig;
    }
  }
};
